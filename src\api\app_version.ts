import request from '@/utils/request'

// APP版本列表
export function apiAppVersionLists(params: any) {
    return request.get({ url: '/app_version/lists', params })
}

// APP版本添加/编辑
export function apiAppVersionAdd(params: any) {
    return request.post({ url: '/app_version/add', params })
}

// APP版本上/下架
export function apiAppVersionChange(params: any) {
    return request.get({ url: '/app_version/change', params })
}

// APP版本删除
export function apiAppVersionDelete(params: any) {
    return request.post({ url: '/app_version/delete', params })
}

// APP版本详情（用于编辑时获取数据）
export function apiAppVersionDetail(params: any) {
    return request.get({ url: '/app_version/detail', params })
}
