<template>
    <div class="index-lists">
        <el-card class="!border-none" shadow="never">
            <el-form ref="formRef" class="mb-[-16px]" :model="queryParams" :inline="true">
                <el-form-item label="平台类型">
                    <el-select v-model="queryParams.platform" style="width: 160px;" placeholder="请选择平台类型">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="Android" :value="1"></el-option>
                        <el-option label="iOS" :value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="包类型">
                    <el-select v-model="queryParams.package_type" style="width: 160px;" placeholder="请选择包类型">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="安装包" :value="1"></el-option>
                        <el-option label="升级包" :value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="queryParams.status" style="width: 160px;" placeholder="请选择状态">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="上架" :value="1"></el-option>
                        <el-option label="下架" :value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none" shadow="never">
            <div class="mb-4">
                <el-button
                    v-perms="['setting.app_version/add']"
                    type="primary"
                    @click="handleAdd"
                >
                    <template #icon>
                        <icon name="el-icon-Plus" />
                    </template>
                    发布
                </el-button>
            </div>
            <el-table :data="lists" size="large" v-loading="pager.loading">
                <el-table-column label="ID" prop="id" min-width="80" />
                <el-table-column label="平台类型" min-width="100">
                    <template #default="{ row }">
                        <div>{{ row.platform == 1 ? 'Android' : row.platform == 2 ? 'iOS' : '-' }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="版本名称" prop="version_name" min-width="120" />
                <el-table-column label="版本号" prop="version_code" min-width="100" />
                <el-table-column label="包类型" min-width="100">
                    <template #default="{ row }">
                        <div>{{ row.package_type == 1 ? '安装包' : row.package_type == 2 ? '升级包' : '-' }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="下载地址" min-width="200">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <div class="truncate max-w-[150px]" :title="row.download_url">
                                {{ row.download_url || '-' }}
                            </div>
                            <el-button 
                                v-if="row.download_url"
                                type="text" 
                                size="small" 
                                @click="copyUrl(row.download_url)"
                                class="ml-2"
                            >
                                复制
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="更新日志" min-width="200">
                    <template #default="{ row }">
                        <el-tooltip 
                            :content="row.update_log" 
                            placement="top" 
                            :disabled="!row.update_log || row.update_log.length <= 50"
                        >
                            <div class="truncate max-w-[180px]">
                                {{ row.update_log || '-' }}
                            </div>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column label="状态" min-width="100">
                    <template #default="{ row }">
                        <el-tag :type="row.status == 1 ? 'success' : 'danger'">
                            {{ row.status == 1 ? '上架' : '下架' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="create_time" min-width="160" />
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            v-perms="['setting.app_version/edit']"
                            type="primary"
                            link
                            @click="handleEdit(row)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            v-if="row.status == 0"
                            v-perms="['setting.app_version/up']"
                            type="success"
                            link
                            @click="handleChangeStatus(row, 1)"
                        >
                            上架
                        </el-button>
                        <el-button
                            v-if="row.status == 1"
                            v-perms="['setting.app_version/down']"
                            type="warning"
                            link
                            @click="handleChangeStatus(row, 0)"
                        >
                            下架
                        </el-button>
                        <el-button
                            v-perms="['setting.app_version/delete']"
                            type="danger"
                            link
                            @click="handleDelete(row.id)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex mt-4 justify-end">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup name="appVersionLists">
import { apiAppVersionLists, apiAppVersionChange, apiAppVersionDelete } from '@/api/app_version'
import { usePaging } from '@/hooks/usePaging'
import feedback from '@/utils/feedback'
import { useRouter } from 'vue-router'
import { reactive, computed } from 'vue'

const router = useRouter()

// 查询参数
const queryParams = reactive({
    platform: '',
    package_type: '',
    status: ''
})

// 分页相关
const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: apiAppVersionLists,
    params: queryParams
})

// 列表数据
const lists = computed(() => pager.lists)

// 复制链接
const copyUrl = async (url: string) => {
    try {
        await navigator.clipboard.writeText(url)
        feedback.msgSuccess('复制成功')
    } catch (err) {
        feedback.msgError('复制失败')
    }
}

// 添加
const handleAdd = () => {
    router.push('/setting/app_version/edit')
}

// 编辑
const handleEdit = (row: any) => {
    router.push(`/setting/app_version/edit?id=${row.id}`)
}

// 上/下架
const handleChangeStatus = async (row: any, status: number) => {
    const action = status === 1 ? '上架' : '下架'
    await feedback.confirm(`确定要${action}该版本吗？`)
    await apiAppVersionChange({ id: row.id, status })
    feedback.msgSuccess(`${action}成功`)
    getLists()
}

// 删除
const handleDelete = async (id: number) => {
    await feedback.confirm('确定要删除该版本吗？')
    await apiAppVersionDelete({ id })
    feedback.msgSuccess('删除成功')
    getLists()
}

// 初始化
getLists()
</script>
