<template>
    <el-card shadow="never" class="!border-none">
        <el-page-header :content="pageTitle" @back="$router.back()" />
    </el-card>

    <el-card shadow="never" style="margin-top: 15px" class="!border-none">
        <div class="pl-[80px] pr-[100px]">
            <el-form
                ref="formRef"
                :model="formData"
                :rules="rules"
                label-width="120px"
                class="mt-6"
            >
                <el-form-item label="平台类型" prop="platform">
                    <el-select v-model="formData.platform" style="width: 320px;" placeholder="请选择平台类型">
                        <el-option label="Android" :value="1"></el-option>
                        <el-option label="iOS" :value="2"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="包类型" prop="package_type">
                    <el-select v-model="formData.package_type" style="width: 320px;" placeholder="请选择包类型">
                        <el-option label="安装包" :value="1"></el-option>
                        <el-option label="升级包" :value="2"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="版本名称" prop="version_name">
                    <el-input
                        v-model="formData.version_name"
                        placeholder="请输入版本名称，如：1.0.0"
                        maxlength="20"
                        show-word-limit
                        class="w-80"
                    />
                </el-form-item>

                <el-form-item label="版本号" prop="version_code">
                    <el-input-number
                        v-model="formData.version_code"
                        :min="1"
                        placeholder="请输入版本号"
                        class="w-80"
                        controls-position="right"
                    />
                </el-form-item>

                <el-form-item label="安装包文件" prop="download_url">
                    <div class="w-80">
                        <el-upload
                            ref="uploadRef"
                            :action="uploadAction"
                            :headers="uploadHeaders"
                            :show-file-list="false"
                            :before-upload="beforeUpload"
                            :on-success="handleUploadSuccess"
                            :on-error="handleUploadError"
                            :on-progress="handleUploadProgress"
                            accept=".apk,.ipa,wgt"
                            name="file"
                        >
                            <el-button type="primary" :loading="uploading">
                                {{ uploading ? '上传中...' : '选择文件' }}
                            </el-button>
                        </el-upload>
                        <div v-if="formData.download_url" class="mt-2 flex items-center">
                            <el-icon class="mr-1"><Document /></el-icon>
                            <span class="text-sm text-gray-600">{{ fileName }}</span>
                            <el-button type="text" size="small" @click="removeFile" class="ml-2">
                                删除
                            </el-button>
                        </div>
                        <div class="form-tips mt-2">
                            请上传APP安装包文件（.apk/.ipa格式），文件大小不超过100MB
                        </div>
                        <el-progress
                            v-if="uploading"
                            :percentage="uploadProgress"
                            class="mt-2"
                        />
                    </div>
                </el-form-item>

                <el-form-item label="更新日志" prop="update_log">
                    <el-input
                        v-model="formData.update_log"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入更新日志"
                        maxlength="500"
                        show-word-limit
                        class="w-80"
                    />
                </el-form-item>

                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="formData.status">
                        <el-radio :label="1">上架</el-radio>
                        <el-radio :label="0">下架</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </div>
    </el-card>

    <footer-btns>
        <el-button @click="$router.back()">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '发布' }}
        </el-button>
    </footer-btns>
</template>

<script lang="ts" setup name="appVersionEdit">
import { apiAppVersionAdd, apiAppVersionDetail } from '@/api/app_version'
import feedback from '@/utils/feedback'
import type { ElForm, ElUpload } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { reactive, ref, computed, onMounted } from 'vue'
import FooterBtns from '@/components/footer-btns/index.vue'
import useUserStore from '@/stores/modules/user'
import config from '@/config'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 是否为编辑模式
const id = route.query.id as string
const isEdit = computed(() => !!id)
const pageTitle = computed(() => isEdit.value ? '编辑版本' : '发布版本')
const loading = ref(false)

// 上传相关
const uploadRef = ref()
const uploading = ref(false)
const uploadProgress = ref(0)
const fileName = ref('')
const uploadAction = computed(() => `${config.baseUrl}${config.urlPrefix}/upload/file`)
const uploadHeaders = computed(() => ({
    token: userStore.token,
    version: config.version
}))

// 表单引用
type FormInstance = InstanceType<typeof ElForm>
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
    id: id || '',
    platform: '',
    package_type: '',
    version_name: '',
    version_code: '',
    download_url: '',
    update_log: '',
    status: 1
})

// 表单验证规则
const rules = {
    platform: [{ required: true, message: '请选择平台类型', trigger: 'change' }],
    package_type: [{ required: true, message: '请选择包类型', trigger: 'change' }],
    version_name: [{ required: true, message: '请输入版本名称', trigger: 'blur' }],
    version_code: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
    download_url: [
        { required: true, message: '请上传安装包文件', trigger: 'change' }
    ],
    update_log: [{ required: true, message: '请输入更新日志', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],
    is_force: [{ required: true, message: '请选择是否强制更新', trigger: 'change' }]
}

// 文件上传处理函数
const beforeUpload = (file: File) => {
    const isValidType = file.name.endsWith('.apk') || file.name.endsWith('.ipa')
    const isLt100M = file.size / 1024 / 1024 < 100

    if (!isValidType) {
        feedback.msgError('只能上传 .apk 或 .ipa 格式的文件!')
        return false
    }
    if (!isLt100M) {
        feedback.msgError('上传文件大小不能超过 100MB!')
        return false
    }

    uploading.value = true
    uploadProgress.value = 0
    fileName.value = file.name
    return true
}

const handleUploadProgress = (event: any) => {
    uploadProgress.value = Math.round((event.loaded / event.total) * 100)
}

const handleUploadSuccess = (response: any) => {
    uploading.value = false
    uploadProgress.value = 0

    if (response.code === 1) {
        formData.download_url = response.data.uri
        fileName.value = response.data.name || fileName.value
        feedback.msgSuccess('文件上传成功')
    } else {
        feedback.msgError(response.msg || '上传失败')
    }
}

const handleUploadError = () => {
    uploading.value = false
    uploadProgress.value = 0
    feedback.msgError('文件上传失败')
}

const removeFile = () => {
    formData.download_url = ''
    fileName.value = ''
}

// 获取详情数据
const getDetail = async () => {
    if (!isEdit.value) return

    try {
        const data = await apiAppVersionDetail({ id })
        Object.assign(formData, data)
        // 如果有下载地址，提取文件名
        if (data.download_url) {
            const urlParts = data.download_url.split('/')
            fileName.value = urlParts[urlParts.length - 1]
        }
    } catch (error) {
        feedback.msgError('获取详情失败')
        router.back()
    }
}

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return
    
    await formRef.value.validate()
    
    try {
        loading.value = true
        await apiAppVersionAdd(formData)
        feedback.msgSuccess(isEdit.value ? '更新成功' : '发布成功')
        router.back()
    } catch (error) {
        feedback.msgError(isEdit.value ? '更新失败' : '发布失败')
    } finally {
        loading.value = false
    }
}

// 初始化
onMounted(() => {
    getDetail()
})
</script>
