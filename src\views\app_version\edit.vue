<template>
    <el-card shadow="never" class="!border-none">
        <el-page-header :content="pageTitle" @back="$router.back()" />
    </el-card>

    <el-card shadow="never" style="margin-top: 15px" class="!border-none">
        <div class="pl-[80px] pr-[100px]">
            <el-form
                ref="formRef"
                :model="formData"
                :rules="rules"
                label-width="120px"
                class="mt-6"
            >
                <el-form-item label="平台类型" prop="platform">
                    <el-select v-model="formData.platform" style="width: 320px;" placeholder="请选择平台类型">
                        <el-option label="Android" :value="1"></el-option>
                        <el-option label="iOS" :value="2"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="包类型" prop="package_type">
                    <el-select v-model="formData.package_type" style="width: 320px;" placeholder="请选择包类型">
                        <el-option label="安装包" :value="1"></el-option>
                        <el-option label="升级包" :value="2"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="版本名称" prop="version_name">
                    <el-input
                        v-model="formData.version_name"
                        placeholder="请输入版本名称，如：1.0.0"
                        maxlength="20"
                        show-word-limit
                        class="w-80"
                    />
                </el-form-item>

                <el-form-item label="版本号" prop="version_code">
                    <el-input-number
                        v-model="formData.version_code"
                        :min="1"
                        placeholder="请输入版本号"
                        class="w-80"
                        controls-position="right"
                    />
                </el-form-item>

                <el-form-item label="安装包文件" prop="download_url">
                    <div class="w-80">
                        <material-picker
                            v-model="formData.download_url"
                            :limit="1"
                            type="file"
                        />
                        <div class="form-tips mt-2">
                            请上传APP安装包文件（.apk/.ipa格式）
                        </div>
                    </div>
                </el-form-item>

                <el-form-item label="更新日志" prop="update_log">
                    <el-input
                        v-model="formData.update_log"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入更新日志"
                        maxlength="500"
                        show-word-limit
                        class="w-80"
                    />
                </el-form-item>

                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="formData.status">
                        <el-radio :label="1">上架</el-radio>
                        <el-radio :label="0">下架</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="是否强制更新" prop="is_force">
                    <el-radio-group v-model="formData.is_force">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </div>
    </el-card>

    <footer-btns>
        <el-button @click="$router.back()">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '发布' }}
        </el-button>
    </footer-btns>
</template>

<script lang="ts" setup name="appVersionEdit">
import { apiAppVersionAdd, apiAppVersionDetail } from '@/api/app_version'
import feedback from '@/utils/feedback'
import type { ElForm } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { reactive, ref, computed, onMounted } from 'vue'
import FooterBtns from '@/components/footer-btns/index.vue'
import MaterialPicker from '@/components/material/picker.vue'

const route = useRoute()
const router = useRouter()

// 是否为编辑模式
const id = route.query.id as string
const isEdit = computed(() => !!id)
const pageTitle = computed(() => isEdit.value ? '编辑版本' : '发布版本')
const loading = ref(false)

// 表单引用
type FormInstance = InstanceType<typeof ElForm>
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
    id: id || '',
    platform: '',
    package_type: '',
    version_name: '',
    version_code: '',
    download_url: '',
    update_log: '',
    status: 1,
    is_force: 0
})

// 表单验证规则
const rules = {
    platform: [{ required: true, message: '请选择平台类型', trigger: 'change' }],
    package_type: [{ required: true, message: '请选择包类型', trigger: 'change' }],
    version_name: [{ required: true, message: '请输入版本名称', trigger: 'blur' }],
    version_code: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
    download_url: [
        { required: true, message: '请上传安装包文件', trigger: 'change' }
    ],
    update_log: [{ required: true, message: '请输入更新日志', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],
    is_force: [{ required: true, message: '请选择是否强制更新', trigger: 'change' }]
}

// 获取详情数据
const getDetail = async () => {
    if (!isEdit.value) return

    try {
        const data = await apiAppVersionDetail({ id })
        Object.assign(formData, data)
    } catch (error) {
        feedback.msgError('获取详情失败')
        router.back()
    }
}

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return
    
    await formRef.value.validate()
    
    try {
        loading.value = true
        await apiAppVersionAdd(formData)
        feedback.msgSuccess(isEdit.value ? '更新成功' : '发布成功')
        router.back()
    } catch (error) {
        feedback.msgError(isEdit.value ? '更新失败' : '发布失败')
    } finally {
        loading.value = false
    }
}

// 初始化
onMounted(() => {
    getDetail()
})
</script>
